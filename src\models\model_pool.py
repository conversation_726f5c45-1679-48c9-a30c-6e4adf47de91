"""
Model pool for managing multiple speech recognition models.
"""

import asyncio
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Union
from enum import Enum

from ..utils.logging import LoggerMixin
from ..utils.metrics import metrics_collector
from .base import BaseModel, ModelState, TranscriptionResult
from .faster_whisper_model import FasterWhisperModel
from .distil_whisper_model import DistilWhisperModel


class ModelSelectionStrategy(Enum):
    """Model selection strategies."""
    FASTEST = "fastest"                # Select fastest model
    MOST_ACCURATE = "most_accurate"   # Select most accurate model
    BALANCED = "balanced"              # Balance speed and accuracy
    LEAST_LOADED = "least_loaded"     # Select least used model
    ROUND_ROBIN = "round_robin"       # Rotate between models


@dataclass
class ModelPoolConfig:
    """Configuration for model pool."""
    primary_model: Dict[str, Any]
    fallback_model: Dict[str, Any]
    pool_size: int = 2
    warm_start: bool = True
    selection_strategy: ModelSelectionStrategy = ModelSelectionStrategy.BALANCED
    max_idle_time_s: int = 300  # Unload models after 5 minutes of inactivity
    memory_limit_mb: int = 2048  # Maximum total memory usage
    preload_timeout_s: int = 60
    health_check_interval_s: int = 30


class ModelPool(LoggerMixin):
    """Pool of speech recognition models with automatic selection and management."""
    
    def __init__(self, config: ModelPoolConfig):
        """Initialize model pool.
        
        Args:
            config: Model pool configuration
        """
        super().__init__()
        self.config = config
        
        # Model storage
        self._models: Dict[str, BaseModel] = {}
        self._loading_models: Dict[str, asyncio.Task] = {}
        
        # Selection state
        self._current_model: Optional[str] = None
        self._round_robin_index = 0
        self._model_usage_count: Dict[str, int] = {}
        
        # Pool management
        self._health_check_task: Optional[asyncio.Task] = None
        self._is_running = False
        
        # Performance tracking
        self._transcription_history: List[Dict[str, Any]] = []
        self._max_history = 1000
        
    async def start(self) -> None:
        """Start the model pool."""
        if self._is_running:
            return
        
        self.logger.info("Starting model pool")
        self._is_running = True
        
        try:
            # Initialize models
            await self._initialize_models()
            
            # Start health check task
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            
            self.logger.info("Model pool started", 
                           loaded_models=len([m for m in self._models.values() if m.is_loaded()]))
            
        except Exception as e:
            self.logger.error("Failed to start model pool", error=str(e))
            self._is_running = False
            raise
    
    async def stop(self) -> None:
        """Stop the model pool."""
        if not self._is_running:
            return
        
        self.logger.info("Stopping model pool")
        self._is_running = False
        
        # Cancel health check
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # Cancel loading tasks
        for task in self._loading_models.values():
            task.cancel()
        
        if self._loading_models:
            await asyncio.gather(*self._loading_models.values(), return_exceptions=True)
        self._loading_models.clear()
        
        # Unload all models
        for model in self._models.values():
            try:
                await model.unload()
            except Exception as e:
                self.logger.error("Error unloading model", model=model.model_name, error=str(e))
        
        self.logger.info("Model pool stopped")
    
    async def _initialize_models(self) -> None:
        """Initialize models based on configuration."""
        # Create primary model
        primary_config = self.config.primary_model
        primary_model = await self._create_model(primary_config, "primary")
        
        # Create fallback model
        fallback_config = self.config.fallback_model
        fallback_model = await self._create_model(fallback_config, "fallback")
        
        # Warm start models if configured
        if self.config.warm_start:
            await self._warm_start_models()
    
    async def _create_model(self, model_config: Dict[str, Any], model_role: str) -> BaseModel:
        """Create a model from configuration.
        
        Args:
            model_config: Model configuration
            model_role: Role of the model (primary, fallback, etc.)
            
        Returns:
            Created model instance
        """
        model_name = model_config.get("name", "").lower()
        
        if model_name == "faster-whisper":
            model = FasterWhisperModel(
                model_size=model_config.get("model_size", "base"),
                device=model_config.get("device", "auto"),
                compute_type=model_config.get("compute_type", "float16"),
                cpu_threads=model_config.get("cpu_threads", 0)
            )
        elif model_name == "distil-whisper":
            model = DistilWhisperModel(
                model_size=model_config.get("model_size", "distil-small.en"),
                device=model_config.get("device", "auto"),
                torch_dtype=model_config.get("torch_dtype", "float16")
            )
        else:
            raise ValueError(f"Unknown model type: {model_name}")
        
        # Store model with role-based key
        model_key = f"{model_role}_{model.model_name}"
        self._models[model_key] = model
        self._model_usage_count[model_key] = 0
        
        self.logger.info("Created model", 
                        model_key=model_key,
                        model_type=model.model_type.value)
        
        return model
    
    async def _warm_start_models(self) -> None:
        """Warm start (preload) models."""
        self.logger.info("Warm starting models")
        
        load_tasks = []
        for model_key, model in self._models.items():
            if not model.is_loaded() and not model.is_loading():
                task = asyncio.create_task(self._load_model_safe(model_key))
                load_tasks.append(task)
                self._loading_models[model_key] = task
        
        if load_tasks:
            # Wait for all models to load or timeout
            try:
                await asyncio.wait_for(
                    asyncio.gather(*load_tasks, return_exceptions=True),
                    timeout=self.config.preload_timeout_s
                )
            except asyncio.TimeoutError:
                self.logger.warning("Model warm start timed out")
            
            # Clean up loading tasks
            for model_key in list(self._loading_models.keys()):
                if self._loading_models[model_key].done():
                    del self._loading_models[model_key]
    
    async def _load_model_safe(self, model_key: str) -> bool:
        """Safely load a model with error handling.
        
        Args:
            model_key: Model key
            
        Returns:
            True if successful, False otherwise
        """
        try:
            model = self._models[model_key]
            await model.load()
            self.logger.info("Model loaded successfully", model_key=model_key)
            return True
        except Exception as e:
            self.logger.error("Failed to load model", model_key=model_key, error=str(e))
            return False
    
    async def transcribe(self, 
                        audio_data, 
                        sample_rate: int = 16000,
                        **kwargs) -> TranscriptionResult:
        """Transcribe audio using the best available model.
        
        Args:
            audio_data: Audio samples
            sample_rate: Sample rate
            **kwargs: Additional transcription parameters
            
        Returns:
            Transcription result
        """
        if not self._is_running:
            raise RuntimeError("Model pool is not running")
        
        # Select best model
        model_key = await self._select_model(**kwargs)
        
        if not model_key:
            raise RuntimeError("No suitable model available")
        
        model = self._models[model_key]
        
        # Ensure model is loaded
        if not model.is_loaded():
            if model_key in self._loading_models:
                # Wait for loading to complete
                await self._loading_models[model_key]
            else:
                # Load model now
                loading_task = asyncio.create_task(self._load_model_safe(model_key))
                self._loading_models[model_key] = loading_task
                await loading_task
                
                if model_key in self._loading_models:
                    del self._loading_models[model_key]
        
        if not model.is_loaded():
            raise RuntimeError(f"Failed to load model: {model_key}")
        
        # Perform transcription
        start_time = time.time()
        
        try:
            with metrics_collector.measure_latency("transcription"):
                result = await model._safe_transcribe(audio_data, sample_rate, **kwargs)
            
            # Update usage statistics
            self._model_usage_count[model_key] += 1
            self._current_model = model_key
            
            # Store transcription history
            self._transcription_history.append({
                "model_key": model_key,
                "timestamp": start_time,
                "processing_time_ms": result.processing_time_ms,
                "confidence": result.confidence,
                "text_length": len(result.text),
                "success": True
            })
            
            # Trim history if too long
            if len(self._transcription_history) > self._max_history:
                self._transcription_history = self._transcription_history[-self._max_history:]
            
            self.logger.debug("Transcription completed",
                            model_key=model_key,
                            processing_time_ms=result.processing_time_ms,
                            confidence=result.confidence)
            
            return result
            
        except Exception as e:
            # Record failure
            self._transcription_history.append({
                "model_key": model_key,
                "timestamp": start_time,
                "processing_time_ms": (time.time() - start_time) * 1000,
                "confidence": 0.0,
                "text_length": 0,
                "success": False,
                "error": str(e)
            })
            
            self.logger.error("Transcription failed", model_key=model_key, error=str(e))
            
            # Try fallback model if available
            if model_key.startswith("primary"):
                fallback_key = self._find_fallback_model()
                if fallback_key and fallback_key != model_key:
                    self.logger.info("Attempting fallback model", fallback_model=fallback_key)
                    try:
                        return await self.transcribe(audio_data, sample_rate, **kwargs)
                    except Exception as fallback_error:
                        self.logger.error("Fallback model also failed", error=str(fallback_error))
            
            raise
    
    async def _select_model(self, **kwargs) -> Optional[str]:
        """Select the best model for transcription.
        
        Args:
            **kwargs: Transcription parameters that might influence selection
            
        Returns:
            Selected model key or None
        """
        available_models = [
            key for key, model in self._models.items() 
            if model.is_loaded() or not model.state == ModelState.ERROR
        ]
        
        if not available_models:
            return None
        
        strategy = self.config.selection_strategy
        
        if strategy == ModelSelectionStrategy.FASTEST:
            return self._select_fastest_model(available_models)
        elif strategy == ModelSelectionStrategy.MOST_ACCURATE:
            return self._select_most_accurate_model(available_models)
        elif strategy == ModelSelectionStrategy.BALANCED:
            return self._select_balanced_model(available_models)
        elif strategy == ModelSelectionStrategy.LEAST_LOADED:
            return self._select_least_loaded_model(available_models)
        elif strategy == ModelSelectionStrategy.ROUND_ROBIN:
            return self._select_round_robin_model(available_models)
        else:
            # Default to first available
            return available_models[0]
    
    def _select_fastest_model(self, available_models: List[str]) -> str:
        """Select the fastest model based on historical performance."""
        if not self._transcription_history:
            # Prefer Distil-Whisper for speed
            for model_key in available_models:
                if "distil" in model_key.lower():
                    return model_key
            return available_models[0]
        
        # Calculate average processing time per model
        model_times = {}
        for record in self._transcription_history[-100:]:  # Recent history
            if record["success"]:
                model_key = record["model_key"]
                if model_key not in model_times:
                    model_times[model_key] = []
                model_times[model_key].append(record["processing_time_ms"])
        
        # Find model with lowest average time
        best_model = None
        best_time = float('inf')
        
        for model_key in available_models:
            if model_key in model_times and model_times[model_key]:
                avg_time = sum(model_times[model_key]) / len(model_times[model_key])
                if avg_time < best_time:
                    best_time = avg_time
                    best_model = model_key
        
        return best_model or available_models[0]
    
    def _select_most_accurate_model(self, available_models: List[str]) -> str:
        """Select the most accurate model based on confidence scores."""
        if not self._transcription_history:
            # Prefer Faster-Whisper for accuracy
            for model_key in available_models:
                if "faster" in model_key.lower():
                    return model_key
            return available_models[0]
        
        # Calculate average confidence per model
        model_confidence = {}
        for record in self._transcription_history[-100:]:
            if record["success"]:
                model_key = record["model_key"]
                if model_key not in model_confidence:
                    model_confidence[model_key] = []
                model_confidence[model_key].append(record["confidence"])
        
        # Find model with highest average confidence
        best_model = None
        best_confidence = 0.0
        
        for model_key in available_models:
            if model_key in model_confidence and model_confidence[model_key]:
                avg_confidence = sum(model_confidence[model_key]) / len(model_confidence[model_key])
                if avg_confidence > best_confidence:
                    best_confidence = avg_confidence
                    best_model = model_key
        
        return best_model or available_models[0]
    
    def _select_balanced_model(self, available_models: List[str]) -> str:
        """Select model balancing speed and accuracy."""
        if not self._transcription_history:
            return available_models[0]
        
        # Calculate composite score (weighted speed and accuracy)
        model_scores = {}
        
        for model_key in available_models:
            recent_records = [
                r for r in self._transcription_history[-100:] 
                if r["model_key"] == model_key and r["success"]
            ]
            
            if not recent_records:
                model_scores[model_key] = 0.5  # Neutral score
                continue
            
            # Calculate normalized speed score (lower time = higher score)
            avg_time = sum(r["processing_time_ms"] for r in recent_records) / len(recent_records)
            speed_score = max(0, (1000 - avg_time) / 1000)  # Normalize to 0-1
            
            # Calculate accuracy score
            avg_confidence = sum(r["confidence"] for r in recent_records) / len(recent_records)
            accuracy_score = avg_confidence
            
            # Weighted composite score (60% accuracy, 40% speed)
            composite_score = 0.6 * accuracy_score + 0.4 * speed_score
            model_scores[model_key] = composite_score
        
        # Select model with highest composite score
        best_model = max(model_scores.keys(), key=lambda k: model_scores[k])
        return best_model
    
    def _select_least_loaded_model(self, available_models: List[str]) -> str:
        """Select the least used model."""
        least_used = min(available_models, key=lambda k: self._model_usage_count[k])
        return least_used
    
    def _select_round_robin_model(self, available_models: List[str]) -> str:
        """Select model using round-robin strategy."""
        selected = available_models[self._round_robin_index % len(available_models)]
        self._round_robin_index += 1
        return selected
    
    def _find_fallback_model(self) -> Optional[str]:
        """Find a fallback model."""
        for model_key in self._models:
            if "fallback" in model_key and self._models[model_key].is_loaded():
                return model_key
        return None
    
    async def _health_check_loop(self) -> None:
        """Periodic health check and maintenance."""
        while self._is_running:
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.config.health_check_interval_s)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Health check error", error=str(e))
                await asyncio.sleep(self.config.health_check_interval_s)
    
    async def _perform_health_check(self) -> None:
        """Perform health check and maintenance tasks."""
        current_time = time.time()
        
        # Check for idle models to unload
        models_to_unload = []
        total_memory_mb = 0
        
        for model_key, model in self._models.items():
            if model.is_loaded():
                memory_mb = model.get_memory_usage_mb()
                total_memory_mb += memory_mb
                
                idle_time = model.get_idle_time()
                if idle_time > self.config.max_idle_time_s:
                    models_to_unload.append(model_key)
        
        # Unload idle models
        for model_key in models_to_unload:
            try:
                await self._models[model_key].unload()
                self.logger.info("Unloaded idle model", model_key=model_key)
            except Exception as e:
                self.logger.error("Error unloading idle model", model_key=model_key, error=str(e))
        
        # Check memory usage
        if total_memory_mb > self.config.memory_limit_mb:
            self.logger.warning("Memory usage exceeds limit", 
                              current_mb=total_memory_mb,
                              limit_mb=self.config.memory_limit_mb)
            
            # Unload least used model
            loaded_models = [k for k, m in self._models.items() if m.is_loaded()]
            if loaded_models:
                least_used = min(loaded_models, key=lambda k: self._model_usage_count[k])
                try:
                    await self._models[least_used].unload()
                    self.logger.info("Unloaded model to free memory", model_key=least_used)
                except Exception as e:
                    self.logger.error("Error unloading model for memory", error=str(e))
        
        # Update metrics
        metrics_collector.set_gauge("model_pool_memory_mb", total_memory_mb)
        metrics_collector.set_gauge("model_pool_loaded_count", 
                                   len([m for m in self._models.values() if m.is_loaded()]))
    
    def get_stats(self) -> Dict[str, Any]:
        """Get model pool statistics.
        
        Returns:
            Dictionary of statistics
        """
        loaded_models = [k for k, m in self._models.items() if m.is_loaded()]
        loading_models = list(self._loading_models.keys())
        total_memory = sum(m.get_memory_usage_mb() for m in self._models.values() if m.is_loaded())
        
        # Calculate recent performance
        recent_transcriptions = self._transcription_history[-100:]
        successful_transcriptions = [r for r in recent_transcriptions if r["success"]]
        
        avg_processing_time = 0.0
        avg_confidence = 0.0
        success_rate = 0.0
        
        if successful_transcriptions:
            avg_processing_time = sum(r["processing_time_ms"] for r in successful_transcriptions) / len(successful_transcriptions)
            avg_confidence = sum(r["confidence"] for r in successful_transcriptions) / len(successful_transcriptions)
        
        if recent_transcriptions:
            success_rate = len(successful_transcriptions) / len(recent_transcriptions)
        
        return {
            "is_running": self._is_running,
            "total_models": len(self._models),
            "loaded_models": len(loaded_models),
            "loading_models": len(loading_models),
            "current_model": self._current_model,
            "total_memory_mb": total_memory,
            "memory_limit_mb": self.config.memory_limit_mb,
            "model_usage_count": self._model_usage_count.copy(),
            "selection_strategy": self.config.selection_strategy.value,
            "performance": {
                "total_transcriptions": len(self._transcription_history),
                "recent_transcriptions": len(recent_transcriptions),
                "success_rate": success_rate,
                "avg_processing_time_ms": avg_processing_time,
                "avg_confidence": avg_confidence
            },
            "models": {
                key: model.get_stats() 
                for key, model in self._models.items()
            }
        }