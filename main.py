"""
Main entry point for the real-time speech-to-text transcription system.
"""

import asyncio
import signal
import sys
import time
import traceback
from pathlib import Path
from typing import Optional

import click
import torch

from src.utils.config import config_manager
from src.utils.logging import setup_logging, get_logger
from src.utils.metrics import metrics_collector
from src.utils.platform import platform_detector
from src.audio.capture import AudioCapturer, AudioCapturerConfig
from src.models.model_pool import ModelPool, ModelPoolConfig, ModelSelectionStrategy
from src.streaming.stream_processor import StreamProcessor, ProcessingChunk, ProcessingResult
from src.ooda.controller import OODAController
from src.ooda.observers import AudioObserver, PerformanceObserver, SystemObserver, ModelObserver, TranscriptionObserver
from src.ooda.decision_engine import DecisionEngine
from src.ooda.adaptation import AdaptationEngine


class TranscriptionSystem:
    """Main transcription system orchestrator."""
    
    def __init__(self):
        """Initialize the transcription system."""
        self.logger = get_logger(__name__)
        
        # Core components
        self.config = None
        self.audio_capturer: Optional[AudioCapturer] = None
        self.model_pool: Optional[ModelPool] = None
        self.stream_processor: Optional[StreamProcessor] = None
        self.ooda_controller: Optional[OODAController] = None
        
        # OODA components
        self.decision_engine: Optional[DecisionEngine] = None
        self.adaptation_engine: Optional[AdaptationEngine] = None
        self.observers = {}
        
        # System state
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
    async def initialize(self) -> None:
        """Initialize all system components."""
        try:
            self.logger.info("Initializing transcription system")
            
            # Load configuration
            self.config = config_manager.get_config()
            self.logger.info("Configuration loaded", 
                           audio_sample_rate=self.config.audio.sample_rate,
                           target_latency_ms=self.config.performance.target_latency_ms)
            
            # Detect platform capabilities
            platform_info = platform_detector.detect_platform()
            self.logger.info("Platform detected",
                           platform=platform_info.platform_type.value,
                           architecture=platform_info.architecture,
                           low_latency_support=platform_info.supports_low_latency)
            
            # Initialize audio capturer
            await self._initialize_audio_capturer()
            
            # Initialize model pool
            await self._initialize_model_pool()
            
            # Initialize stream processor
            await self._initialize_stream_processor()
            
            # Initialize OODA loop
            await self._initialize_ooda_loop()
            
            # Start metrics server if enabled
            if self.config.system.enable_metrics:
                metrics_collector.start_prometheus_server(self.config.system.metrics_port)
                self.logger.info("Metrics server started", port=self.config.system.metrics_port)
            
            self.logger.info("Transcription system initialized successfully")
            
        except Exception as e:
            self.logger.error("Failed to initialize transcription system", error=str(e))
            await self._cleanup()
            raise
    
    async def _initialize_audio_capturer(self):
        """Initialize audio capture system."""
        self.logger.info("Initializing audio capturer")

        # Get platform-specific backend preference
        platform_info = platform_detector.detect_platform()
        if platform_info.platform_type.value == "windows":
            preferred_backend = self.config.audio_backends.windows.preferred
        elif platform_info.platform_type.value == "linux":
            preferred_backend = self.config.audio_backends.linux.preferred
        elif platform_info.platform_type.value == "darwin":
            preferred_backend = self.config.audio_backends.darwin.preferred
        else:
            preferred_backend = None

        capturer_config = AudioCapturerConfig(
            sample_rate=self.config.audio.sample_rate,
            channels=self.config.audio.channels,
            chunk_size=self.config.audio.chunk_size,
            buffer_duration_ms=self.config.audio.buffer_duration_ms,
            device_name=None,  # Use default device
            preferred_backend=preferred_backend,
            enable_noise_gate=True,
            noise_gate_threshold=self.config.audio.silence_threshold,
            silence_timeout_ms=self.config.audio.silence_duration_ms
        )
        
        self.audio_capturer = AudioCapturer(capturer_config)
        
        # Add audio callback for stream processor
        self.audio_capturer.add_audio_callback(self._audio_data_callback)
        
        self.logger.info("Audio capturer initialized")
    
    async def _initialize_model_pool(self):
        """Initialize model pool with configured models."""
        self.logger.info("Initializing model pool")
        
        pool_config = ModelPoolConfig(
            primary_model=self.config.models.primary.model_dump(),
            fallback_model=self.config.models.fallback.model_dump(),
            pool_size=self.config.models.model_pool_size,
            warm_start=self.config.models.warm_start,
            selection_strategy=ModelSelectionStrategy.BALANCED,
            max_idle_time_s=300,
            memory_limit_mb=2048,
            preload_timeout_s=60,
            health_check_interval_s=30
        )
        
        self.model_pool = ModelPool(pool_config)
        
        self.logger.info("Model pool initialized")
    
    async def _initialize_stream_processor(self):
        """Initialize stream processing pipeline."""
        self.logger.info("Initializing stream processor")
        
        self.stream_processor = StreamProcessor(
            target_latency_ms=self.config.performance.target_latency_ms,
            max_latency_ms=self.config.performance.max_latency_ms,
            chunk_duration_ms=self.config.performance.chunk_overlap_ms,
            overlap_ms=self.config.performance.chunk_overlap_ms,
            min_audio_length_ms=self.config.performance.min_audio_length_ms
        )
        
        # Set input buffer
        self.stream_processor.set_input_buffer(self.audio_capturer.audio_buffer)
        
        # Add result callback
        self.stream_processor.add_result_callback(self._transcription_result_callback)
        
        self.logger.info("Stream processor initialized")
    
    async def _initialize_ooda_loop(self):
        """Initialize OODA loop for adaptive behavior."""
        self.logger.info("Initializing OODA loop")
        
        # Create OODA controller
        self.ooda_controller = OODAController(
            observe_interval_ms=self.config.ooda.observe_interval_ms,
            orient_interval_ms=self.config.ooda.orient_interval_ms,
            decide_interval_ms=self.config.ooda.decide_interval_ms,
            act_interval_ms=self.config.ooda.act_interval_ms
        )
        
        # Create observers
        self.observers['audio'] = AudioObserver(self.audio_capturer)
        self.observers['performance'] = PerformanceObserver()
        self.observers['system'] = SystemObserver()
        self.observers['model'] = ModelObserver(self.model_pool)
        self.observers['transcription'] = TranscriptionObserver(None)  # Set later
        
        # Register observers
        for name, observer in self.observers.items():
            self.ooda_controller.register_observer(name, observer.observe)
        
        # Create decision engine
        self.decision_engine = DecisionEngine(
            adaptation_threshold=self.config.ooda.adaptation_threshold
        )
        
        # Register decision engine
        self.ooda_controller.register_decision_engine("main", self.decision_engine.make_decisions)
        
        # Create adaptation engine
        self.adaptation_engine = AdaptationEngine()
        self.adaptation_engine.set_components(
            audio_capturer=self.audio_capturer,
            model_pool=self.model_pool,
            transcription_pipeline=None,  # Set later if needed
            ooda_controller=self.ooda_controller
        )
        
        # Register adaptation engine
        self.ooda_controller.register_actor("main", self.adaptation_engine.execute_decisions)
        
        self.logger.info("OODA loop initialized")
    
    async def _audio_data_callback(self, audio_data, timestamp):
        """Callback for audio data from capturer.
        
        Args:
            audio_data: Audio samples
            timestamp: Capture timestamp
        """
        # This callback feeds audio to the stream processor
        # The stream processor handles the actual processing
        pass
    
    async def _transcription_result_callback(self, result: ProcessingResult):
        """Callback for transcription results.
        
        Args:
            result: Processing result from stream processor
        """
        # Output transcription result
        if result.text.strip():
            print(f"[{result.timestamp:.2f}] {result.text} (confidence: {result.confidence:.2f})")
            
            # Log performance metrics
            self.logger.debug("Transcription result",
                            chunk_id=result.chunk_id,
                            text_length=len(result.text),
                            confidence=result.confidence,
                            processing_time_ms=result.processing_time_ms)
    
    async def _model_processor_func(self, chunk: ProcessingChunk) -> ProcessingResult:
        """Process audio chunk using model pool.
        
        Args:
            chunk: Audio chunk to process
            
        Returns:
            Processing result
        """
        try:
            # Transcribe using model pool
            transcription_result = await self.model_pool.transcribe(
                chunk.audio_data,
                chunk.sample_rate
            )
            
            # Convert to processing result
            result = ProcessingResult(
                chunk_id=chunk.chunk_id,
                text=transcription_result.text,
                confidence=transcription_result.confidence,
                processing_time_ms=transcription_result.processing_time_ms,
                timestamp=chunk.timestamp,
                segments=transcription_result.segments
            )
            
            return result
            
        except Exception as e:
            self.logger.error("Model processing error", 
                            chunk_id=chunk.chunk_id, 
                            error=str(e))
            
            # Return empty result on error
            return ProcessingResult(
                chunk_id=chunk.chunk_id,
                text="",
                confidence=0.0,
                processing_time_ms=(time.time() - chunk.processing_start_time) * 1000.0,
                timestamp=chunk.timestamp
            )
    
    async def start(self) -> None:
        """Start the transcription system."""
        if self.is_running:
            return
        
        try:
            self.logger.info("Starting transcription system")
            
            # Start model pool
            await self.model_pool.start()
            
            # Start audio capture
            await self.audio_capturer.start_capture()
            
            # Start OODA loop
            ooda_task = asyncio.create_task(self.ooda_controller.start())
            
            # Start stream processor
            processor_task = asyncio.create_task(
                self.stream_processor.start_processing(self._model_processor_func)
            )
            
            self.is_running = True
            self.logger.info("Transcription system started successfully")
            
            # Wait for shutdown signal or tasks to complete
            try:
                await asyncio.gather(ooda_task, processor_task)
            except asyncio.CancelledError:
                self.logger.info("Transcription system cancelled")
            
        except Exception as e:
            self.logger.error("Error starting transcription system", error=str(e))
            await self._cleanup()
            raise
        finally:
            self.is_running = False
    
    async def stop(self) -> None:
        """Stop the transcription system gracefully."""
        if not self.is_running:
            return
        
        self.logger.info("Stopping transcription system")
        
        try:
            # Stop stream processor
            if self.stream_processor:
                await self.stream_processor.stop_processing()
            
            # Stop OODA loop
            if self.ooda_controller:
                await self.ooda_controller.stop()
            
            # Stop audio capture
            if self.audio_capturer:
                await self.audio_capturer.stop_capture()
            
            # Stop model pool
            if self.model_pool:
                await self.model_pool.stop()
            
            self.is_running = False
            self.logger.info("Transcription system stopped")
            
        except Exception as e:
            self.logger.error("Error stopping transcription system", error=str(e))
        finally:
            await self._cleanup()
    
    async def _cleanup(self):
        """Clean up system resources."""
        self.logger.info("Cleaning up system resources")
        
        # Clean up components in reverse order
        components = [
            (self.stream_processor, "stop_processing"),
            (self.ooda_controller, "stop"),
            (self.audio_capturer, "stop_capture"),
            (self.model_pool, "stop")
        ]
        
        for component, stop_method in components:
            if component:
                try:
                    await getattr(component, stop_method)()
                except Exception as e:
                    self.logger.error(f"Error cleaning up {type(component).__name__}", error=str(e))
        
        self.logger.info("System cleanup completed")
    
    def get_system_stats(self) -> dict:
        """Get comprehensive system statistics.
        
        Returns:
            Dictionary of system statistics
        """
        stats = {
            "is_running": self.is_running,
            "timestamp": time.time()
        }
        
        if self.audio_capturer:
            stats["audio"] = self.audio_capturer.get_capture_stats()
        
        if self.model_pool:
            stats["models"] = self.model_pool.get_stats()
        
        if self.stream_processor:
            stats["stream_processor"] = self.stream_processor.get_stats()
        
        if self.ooda_controller:
            stats["ooda"] = self.ooda_controller.get_performance_stats()
        
        if self.decision_engine:
            stats["decisions"] = self.decision_engine.get_decision_stats()
        
        if self.adaptation_engine:
            stats["adaptations"] = self.adaptation_engine.get_adaptation_stats()
        
        # Add metrics summary
        stats["metrics"] = metrics_collector.get_summary()
        
        return stats


# Global system instance
transcription_system = TranscriptionSystem()


async def graceful_shutdown(signal_name):
    """Handle graceful shutdown on signal."""
    logger = get_logger(__name__)
    logger.info(f"Received {signal_name}, shutting down gracefully...")
    
    try:
        await transcription_system.stop()
    except Exception as e:
        logger.error("Error during graceful shutdown", error=str(e))
    
    # Set shutdown event
    transcription_system.shutdown_event.set()


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    if sys.platform != "win32":
        # Unix signals
        for sig in (signal.SIGTERM, signal.SIGINT):
            signal.signal(sig, lambda s, f: asyncio.create_task(graceful_shutdown(sig.name)))
    else:
        # Windows signal handling
        signal.signal(signal.SIGINT, lambda s, f: asyncio.create_task(graceful_shutdown("SIGINT")))


@click.command()
@click.option("--config", "-c", type=click.Path(exists=True), help="Configuration file path")
@click.option("--log-level", "-l", default="INFO", help="Logging level")
@click.option("--log-file", type=click.Path(), help="Log file path")
@click.option("--device", "-d", help="Audio input device name")
@click.option("--model", "-m", help="Primary model to use")
@click.option("--gpu", is_flag=True, help="Enable GPU acceleration")
@click.option("--gpu-device", default="cuda:0", help="GPU device to use (e.g., cuda:0)")
@click.option("--no-ooda", is_flag=True, help="Disable OODA loop")
@click.option("--stats-interval", default=30, help="Statistics report interval (seconds)")
@click.option("--test-mode", is_flag=True, help="Run in test mode (shorter duration)")
def main(config, log_level, log_file, device, model, gpu, gpu_device, no_ooda, stats_interval, test_mode):
    """Real-time speech-to-text transcription system with OODA loop adaptation."""
    
    # Setup logging
    setup_logging(
        log_level=log_level,
        log_file=Path(log_file) if log_file else None,
        enable_json=False,
        enable_colors=True
    )
    
    logger = get_logger(__name__)
    logger.info("Starting AI Narration System")
    
    try:
        # Load custom config if provided
        if config:
            config_manager.config_path = Path(config)
        
        # Override config with command line options
        if device or model or gpu:
            current_config = config_manager.get_config()
            if device:
                # Would need to modify config to set device
                logger.info(f"Using audio device: {device}")
            if model:
                # Would need to modify config to set model
                logger.info(f"Using primary model: {model}")
            if gpu:
                # Validate GPU availability
                if not torch.cuda.is_available():
                    logger.error("GPU acceleration requested but CUDA is not available")
                    logger.info("Available devices: CPU only")
                    return 1

                # Validate specific GPU device
                try:
                    device_id = int(gpu_device.split(':')[1]) if ':' in gpu_device else 0
                    if device_id >= torch.cuda.device_count():
                        logger.error(f"GPU device {gpu_device} not available. Available devices: 0-{torch.cuda.device_count()-1}")
                        return 1
                except (ValueError, IndexError):
                    logger.error(f"Invalid GPU device format: {gpu_device}. Use format 'cuda:0'")
                    return 1

                # Enable GPU acceleration
                gpu_name = torch.cuda.get_device_name(device_id)
                gpu_memory = torch.cuda.get_device_properties(device_id).total_memory / 1024**3
                logger.info(f"GPU acceleration enabled: {gpu_name} ({gpu_memory:.1f}GB) on {gpu_device}")

                # Update config for GPU mode
                current_config.models.primary.device = gpu_device
                current_config.models.primary.compute_type = "float16"
                current_config.models.fallback.device = gpu_device
                # Optimize performance settings for GPU
                current_config.performance.target_latency_ms = 100
                current_config.performance.max_latency_ms = 200
                config_manager._config = current_config
        
        # Setup signal handlers
        setup_signal_handlers()
        
        # Run the async main function
        asyncio.run(run_system(no_ooda, stats_interval, test_mode))
        
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error("Fatal error", error=str(e))
        logger.debug("Traceback:", traceback.format_exc())
        return 1
    
    logger.info("AI Narration System stopped")
    return 0


async def run_system(no_ooda: bool, stats_interval: int, test_mode: bool):
    """Run the transcription system.
    
    Args:
        no_ooda: Whether to disable OODA loop
        stats_interval: Statistics reporting interval
        test_mode: Whether to run in test mode
    """
    logger = get_logger(__name__)
    
    try:
        # Initialize system
        await transcription_system.initialize()
        
        # Disable OODA if requested
        if no_ooda:
            logger.info("OODA loop disabled by user request")
            if transcription_system.ooda_controller:
                transcription_system.ooda_controller.disable_phase(transcription_system.ooda_controller.OODAPhase.DECIDE)
                transcription_system.ooda_controller.disable_phase(transcription_system.ooda_controller.OODAPhase.ACT)
        
        # Start periodic stats reporting
        stats_task = asyncio.create_task(report_stats_periodically(stats_interval))
        
        # Start system
        system_task = asyncio.create_task(transcription_system.start())
        
        # In test mode, run for limited time
        if test_mode:
            logger.info("Running in test mode for 30 seconds")
            await asyncio.sleep(30)
            await transcription_system.stop()
        else:
            # Wait for shutdown signal
            await transcription_system.shutdown_event.wait()
        
        # Cancel stats task
        stats_task.cancel()
        try:
            await stats_task
        except asyncio.CancelledError:
            pass
        
        # Wait for system to stop
        try:
            await system_task
        except asyncio.CancelledError:
            pass
        
    except Exception as e:
        logger.error("Error running system", error=str(e))
        raise


async def report_stats_periodically(interval: int):
    """Report system statistics periodically.
    
    Args:
        interval: Reporting interval in seconds
    """
    logger = get_logger(__name__)
    
    while True:
        try:
            await asyncio.sleep(interval)
            
            if transcription_system.is_running:
                stats = transcription_system.get_system_stats()
                
                # Log key metrics
                audio_stats = stats.get("audio", {})
                model_stats = stats.get("models", {})
                stream_stats = stats.get("stream_processor", {})
                
                logger.info("System Statistics",
                          audio_latency_ms=audio_stats.get("current_latency_ms", 0),
                          buffer_utilization=audio_stats.get("buffer_stats", {}).get("utilization", 0),
                          models_loaded=model_stats.get("loaded_models", 0),
                          total_memory_mb=model_stats.get("total_memory_mb", 0),
                          chunks_processed=stream_stats.get("total_chunks_processed", 0),
                          avg_processing_time_ms=stream_stats.get("performance", {}).get("avg_processing_time_ms", 0))
        
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error("Error reporting stats", error=str(e))


if __name__ == "__main__":
    sys.exit(main())